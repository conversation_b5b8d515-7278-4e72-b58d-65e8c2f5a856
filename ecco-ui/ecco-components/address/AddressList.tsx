import * as React from "react";
import {
    Address,
    AddressedLocationAjaxRepository,
    BLDG_SYMBOL,
    Building,
    BuildingAjaxRepository,
    getGlobalApiClient,
    SessionData,
    SessionDataAjaxRepository,
    SessionDataRepository
} from "ecco-dto";
import {Table} from "react-bootstrap";
import {formatPostcode} from "./validation";
import {keyFirstBy} from "@softwareventures/array";
import {Button, Grid, IconButton} from "@eccosolutions/ecco-mui";
import {SearchInput} from "../inputs/PersonSearchBar";
import {loadChunks} from "../data/entityLoaders";
import {AddressChangeCommand, CommandAjaxRepository, CommandRepository} from "ecco-commands";
import DeleteIcon from "@material-ui/icons/Delete";

/** A limit to those we use for NFA etc (still leaves 100=1000 for other canned addresses) */
const MAX_ADDRESSID_OF_FAKES = 100;

interface Props {
    showBuildings: boolean;
    postCode?: string | undefined;
    onChange?: ((addressId: number, buildingId: number | null) => void) | undefined;
    /** Callback if user clicks "create new" after finding no match. Link isn't show if omitted */
    onCreateNew?: ((postCode: string | undefined) => void) | undefined;
}

interface State {
    postCode: string | undefined;
    addressIndexOrder: number[];
    addressIndex: Map<number, Address>;
    addressWithBuildingIndex: Record<number, Building[]>;
    addressSelected?: Address | undefined;
    buildingIdSelected?: number | undefined;
    alerts: {warning?: string | undefined};
}

/**
 * List of addresses to choose from, ie 'change address' modal
 */
export class AddressList extends React.Component<Props, State> {
    private addressRepository: AddressedLocationAjaxRepository;
    private buildingRepository: BuildingAjaxRepository;
    private commandRepository: CommandRepository;
    private sessionDataRepository: SessionDataRepository;
    private sessionData: SessionData | null = null;

    constructor(props: Props) {
        super(props);
        this.state = {
            postCode: this.props.postCode,
            addressIndex: new Map(),
            addressIndexOrder: [],
            addressWithBuildingIndex: {},
            alerts: {}
        };

        const apiClient = getGlobalApiClient(); // should be set in ServicesContextProvider as that provides the apiClient that we load the parent resources from
        this.addressRepository = new AddressedLocationAjaxRepository(apiClient);
        this.buildingRepository = new BuildingAjaxRepository(apiClient);
        this.commandRepository = new CommandAjaxRepository(apiClient);
        this.sessionDataRepository = new SessionDataAjaxRepository(apiClient);
    }

    public override componentDidMount() {
        this.sessionDataRepository.getSessionData().then((sessionData: SessionData) => {
            this.sessionData = sessionData;
            this.handleSearchClick();
        });
    }

    private handlePostCodeChange = (value: string | null) => {
        this.setState({postCode: formatPostcode(value)});
    };

    private handleSearchClick = () => {
        // NB ClientResidenceControl was used to just change the 'residingAt' via cmd, but used this search in BuildingSelector:
        /*<BuildingSearch
                filterOutParents={this.props.filterOutParents}
                buildingId={this.state.residenceId}
                onChange={residenceId => this.setState({residenceId})}
                primaryOnly={false}
        />;*/

        if (this.state.postCode) {
            if (this.props.showBuildings) {
                // shouldn't be too many addresses per postcode
                this.addressRepository
                    // OPTION 1: find buildings at postcode, and then find all non-building addresses
                    // OPTION 2: find all at postcode, and then see if they are building at those addresses
                    // these could be options whether we are talking about the parent building or the units
                    // OPTION 2: chosen
                    .findAllAddressByPostCode(this.state.postCode)
                    .then(addresses => {
                        if (addresses.length == 0) {
                            this.setState({
                                addressIndex: new Map(),
                                addressIndexOrder: [],
                                addressWithBuildingIndex: {},
                                alerts: {warning: "no address found"}
                            });
                            return;
                        }
                        // load all buildings on each address, chunked
                        // we avoid loading the hierarchy because it will have the same postcode
                        const singlePromise = (ids: number[]) =>
                            this.buildingRepository.findAllBuildingsOfLocationIds(ids);
                        loadChunks(
                            addresses.map(a => a.addressId),
                            singlePromise
                        ).then(buildings => {
                            const buildingsByAddressIds: Record<number, Building[]> = {};
                            buildings.forEach(bldg => {
                                if (!bldg.address) {
                                    return;
                                }
                                const curr = buildingsByAddressIds[bldg.address!.addressId];
                                if (curr) {
                                    curr.push(bldg);
                                } else {
                                    buildingsByAddressIds[bldg.address!.addressId] = [bldg];
                                }
                            });
                            this.setState({
                                addressIndex: keyFirstBy(addresses, a => a.addressId),
                                addressIndexOrder: addresses.map(a => a.addressId),
                                addressWithBuildingIndex: buildingsByAddressIds,
                                alerts: addresses.length == 0 ? {warning: "no address found"} : {}
                            });
                        });
                    });
            } else {
                this.addressRepository
                    .findAllAddressByPostCode(this.state.postCode)
                    .then((addresses: Address[]) => {
                        this.setState({
                            addressWithBuildingIndex: {},
                            addressIndex: keyFirstBy(addresses, a => a.addressId),
                            addressIndexOrder: addresses.map(a => a.addressId),
                            alerts: addresses.length == 0 ? {warning: "no address found"} : {}
                        });
                    });
            }
        }
    };

    private handleSearchKeyDown = (event: {key: string}) => {
        if (event.key === "Enter") {
            this.handleSearchClick();
        }
    };

    private handleAddressSelectClick = (id: number, buildingId?: number | undefined) => {
        const address = this.state.addressIndex.get(id);
        this.setState({
            addressSelected: address,
            buildingIdSelected: buildingId
        });
        this.props.onChange && this.props.onChange(address?.addressId!, buildingId || null);
    };

    private handleAddressDisableClick = (id: number, buildingId?: number | undefined) => {
        const address = this.state.addressIndex.get(id)!;

        const cmd = new AddressChangeCommand("update", id);
        cmd.changeDisabled(false, true);
        this.commandRepository
            .sendCommand(cmd)
            .then(() => {
                const newMap = new Map(this.state.addressIndex);
                newMap.set(id, {...address, disabled: true});
                this.setState({addressIndex: newMap});
            })
            .catch(e => {
                alert("disable address: FAILED: " + e.reason.message); // e.reason.message if wanted to expose to user
                throw e; // allows .fail to log the error on console and throw for NR
            });
    };

    private isSelected(address: Address, buildingId?: number | undefined): boolean {
        const adrSelected = this.state.addressSelected
            ? this.state.addressSelected.addressId == address.addressId
            : false;
        const bldgSelected = this.state.buildingIdSelected == buildingId;
        return adrSelected && bldgSelected;
    }

    /** Render one or more rows for a SINGLE address, which includes any sub-units of a building at this address */
    private renderRowsForAddress = (address: Address) => {
        // if there are buildings at the address, then just show those
        // although this may exclude a valid property next door, however we are going to remove this aspect of buildings in the next piece of work
        // if still showing non '@' addresses it's because the units are a different addressId (but the same address) - so disable
        const bldgsAtAddress = this.state.addressWithBuildingIndex[address.addressId] || [];
        if (bldgsAtAddress.length > 0) {
            return bldgsAtAddress.map(bldg => {
                return (
                    <tr
                        className={this.isSelected(address, bldg.buildingId) ? "info" : undefined}
                        onClick={() =>
                            this.handleAddressSelectClick(address.addressId, bldg.buildingId)
                        }
                        key={address.addressId}
                    >
                        <td>{`${BLDG_SYMBOL}`}</td>
                        <td>{address.address[0]}</td>
                        <td>{address.postcode}</td>
                        {/*<td><i className={this.isSelected(address, bldg.buildingId) ? "fa fa-check-circle selected" : "fa fa-check-circle"}/></td>*/}
                        <td className="text-right">
                            <Button size="small" color="primary" variant="contained">
                                select
                            </Button>
                        </td>
                    </tr>
                );
            });
            // else show the addresses
        } else {
            const isStaticAddress = ["NFA", "UNK", "WHLD"].some(a => a == address.postcode);
            return (
                !address.disabled && (
                    <tr
                        className={this.isSelected(address) ? "info" : undefined}
                        onClick={() => this.handleAddressSelectClick(address.addressId)}
                        key={address.addressId}
                    >
                        <td>&nbsp;</td>
                        <td>{address.address[0]}</td>
                        <td>{address.postcode}</td>
                        <td className="text-right">
                            {!isStaticAddress && this.sessionData?.hasRoleBuildingAdmin() && (
                                <IconButton
                                    size={"small"}
                                    onClick={e => {
                                        e.stopPropagation();
                                        this.handleAddressDisableClick(address.addressId);
                                    }}
                                >
                                    <DeleteIcon />
                                </IconButton>
                            )}
                            <Button size="small" color="primary" variant="contained">
                                select
                            </Button>
                        </td>
                    </tr>
                )
            );
        }
    };

    override render() {
        const numAddresses = Object.keys(this.state.addressIndex).length;
        const AddressTableElement =
            numAddresses > 0 ? (
                <Grid item xs={12}>
                    <Table hover>
                        <thead>
                            <tr>
                                <th>&nbsp;</th>
                                <th>address</th>
                                <th>post code</th>
                                <th>&nbsp;</th>
                            </tr>
                        </thead>
                        <tbody>
                            {this.state.addressIndexOrder.map(id =>
                                this.renderRowsForAddress(this.state.addressIndex.get(id)!)
                            )}
                        </tbody>
                    </Table>
                </Grid>
            ) : null;

        return (
            <Grid container>
                <Grid item xs={12}>
                    <SearchInput
                        resultName="{resultName}"
                        placeholder="post code (or NFA, UNK, WHLD)"
                        value={this.state.postCode || ""}
                        onValueChange={this.handlePostCodeChange}
                        onKeyDown={this.handleSearchKeyDown}
                        onClickSearchBack={() => this.handleSearchKeyDown({key: "Enter"})}
                        searchActive={false} // {this.props.searchActive ? "reset" : "search"}
                        errorText={Object.values(this.state.alerts).join(",")}
                    />
                </Grid>
                {(numAddresses > 0 || this.state.alerts.warning) && this.props.onCreateNew && (
                    <Grid item xs={12}>
                        Not found?{" "}
                        <a onClick={() => this.props.onCreateNew!(this.state.postCode)}>
                            Create a new one
                        </a>
                    </Grid>
                )}
                {AddressTableElement}
            </Grid>
        );
    }
}
